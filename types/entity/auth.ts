export type IdpAppType = 'WEIXINWORK' | 'WECHAT' | 'DINGTALK' | 'WELINK' | 'JINZHI' | 'YUNXIETONG' | 'CAS' | 'WEB';

export type IdpAuthorizationParams = {
  idpAppType: IdpAppType;
  idpId: string;
  ou: string
}

export type NormalAuthorizationParams = {
  userid: string;
  password: string;
}

export type NormalAuthorization = {
  JwtToken: string;
}

export interface UserProfiles {
  actionType: string;
  id: number;
  initType: string;
  isActive: number;
  isDelete: number;
  isMaintenanceMode: number;
  name: string;
  pinyin: string;
  roleList: RoleList[];
  roleUuidList: string[];
  startPage: number;
  teamList: TeamList[];
  teamNameList: string[];
  teamRoleList: any[];
  teamUuidList: string[];
  userAuthList: UserAuthList[];
  userId: string;
  userName: string;
  uuid: string;
  vipLevel: number;
}

interface UserAuthList {
  auth: string;
  authGroup: string;
  authName: string;
  startPage: number;
  userUuid?: string;
}

interface TeamList {
  actionType: string;
  children: any[];
  id: number;
  initType: string;
  lft: number;
  name: string;
  rht: number;
  startPage: number;
  userCount: number;
  uuid: string;
}

interface RoleList {
  description: string;
  id: number;
  initType: string;
  name: string;
  startPage: number;
  teamCount: number;
  userCount: number;
  uuid: string;
}