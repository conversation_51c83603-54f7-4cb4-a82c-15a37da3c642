export interface NLResult<T = any> {
  Status: 'OK' | 'ERROR',
  TimeCost: number;
  Return: T
}

export interface NLPaginationResult<T extends Record<string, Array<any>> = {list: any[]}> {
  Status: 'OK' | 'ERROR';
  TimeCost: number;
  Return: {
    currentPage: number;
    pageCount: number;
    pageSize: number;
    rowNum: number;
  } & T;
}

export type NLPaginationRequest<T = Record<string, any>> = {
  currentPage?: number;
  pageSize?: number;
} & Partial<T>;