import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const base = env.VITE_APP_BASE_PATH || "/";
  const isProduction = mode === "production";

  return {
    base,
    plugins: [react(), tsconfigPaths(), tailwindcss()],
    server: {
      allowedHosts: ['host.docker.internal'],
      open: false,
      host: true,
      port: 9999,
      proxy: {
        "/demo/nl": {
          target: env.VITE_APP_NEATLOGIC_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/demo\/nl/, ""),
        },
        "/gateway": {
          target: 'https://gw.waf.bas.dev.wenet.group',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/gateway/, ""),
        }
      },
    },
    build: {
      target: "es2017",
      minify: "esbuild",
      sourcemap: !isProduction,
      cssCodeSplit: true,
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          manualChunks: {
            "nroad-core": ["react", "react-dom", "react-router"],
            "nroad-ui": [
              "sonner"
            ],
            "nroad-utils": ["axios", "dayjs", "zustand", "crypto-js", "swr"],
          },
        },
      },
    },

    // 优化依赖预构建
    optimizeDeps: {
      include: ["react", "react-dom", "react-router", "axios", "dayjs"],
    },

    // esbuild 优化配置
    esbuild: {
      drop: isProduction ? ["console", "debugger"] : [],
      legalComments: "none",
      target: "es2017",
    },
  }
})
