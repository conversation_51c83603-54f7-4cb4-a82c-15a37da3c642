import type { IdpAuthorizationParams, NormalAuthorization, NormalAuthorizationParams } from "#/entity/auth";
import type { Example } from "#/entity/example"
import axios from '@/utils/axios';


export const getExample = () => axios<Example>({
  url: '/api/v1/example',
  method: 'get',
})

export const getWechatOpenid = (code: string) => axios<string>({
  url: `/binding-service/api/v1/openId/${code}`,
  method: 'get',
  baseURL: '/gateway',
  headers: {
    'x-safe-level': 0
  }
})

export const loginWithIdp = ({idpAppType, idpId, ou}: IdpAuthorizationParams) => {
  return new Promise<{token: string; tokenType: string}>((resolve) => {
    setTimeout(() => {
      const now = new Date().getTime();
      const mockToken = `${ou}-${idpAppType}-${idpId}-${now}`
      resolve({
        token: mockToken,
        tokenType: 'Bearer'
      })
    }, 1000)
  })
}

export const loginWithPassword = (params: NormalAuthorizationParams) => axios<NormalAuthorization>({
  url: '/login/check',
  method: 'POST',
  data: params,
  headers: {
    'x-safe-level': 0
  }
})