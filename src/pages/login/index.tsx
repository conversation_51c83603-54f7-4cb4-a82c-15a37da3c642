import type React from 'react';
import { Card, Divider } from 'antd-mobile';
import { useNavigate, useLocation } from 'react-router';
import { LoginForm } from './components';
import { useAuthStore } from '@/store/authStore';
import type { NormalAuthorizationParams } from '#/entity/auth';
import './index.css';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setAuth } = useAuthStore();
  
  // 从 URL 中获取重定向路径
  const getRedirectPath = () => {
    const params = new URLSearchParams(location.search);
    return params.get('redirect') || '/';
  };

  const handleSubmit = async (values: NormalAuthorizationParams) => {
    try {
      // 这里模拟登录请求
      console.log('登录信息:', values);
      
      // 实际项目中应该调用 API:
      // const res = await loginApi(values.username, values.password);
      // if (res.success) { 
      //   const { token } = res.data;
      //   setAuth(token);
      // }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟登录成功，设置 token
      const mockToken = `mock-jwt-token- + ${Date.now()}`;
      setAuth(mockToken);
      
      // 重定向到来源页面或首页
      const redirectPath = getRedirectPath();
      navigate(redirectPath, { replace: true });
    } catch (error) {
      console.error('登录失败:', error);
      throw error; // 向上传递错误，让表单组件处理加载状态
    }
  };

  return (
    <div className="login-container">
      <Card className="login-card">
        <div className="login-header">
          <h2 className="login-title">欢迎登录</h2>
          <Divider />
        </div>
        <LoginForm onSubmit={handleSubmit} />
      </Card>
    </div>
  );
};

export default LoginPage;
