import type React from 'react';
import { useState } from 'react';
import { Form, Input, Button } from 'antd-mobile';
import { FaUser, FaLock } from 'react-icons/fa';
import { toast } from 'sonner'
import type { NormalAuthorizationParams } from '#/entity/auth';


interface LoginFormProps {
  onSubmit: (values: NormalAuthorizationParams) => Promise<void>;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleFinish = async (values: NormalAuthorizationParams) => {
    setLoading(true);
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFinishFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
    // 显示第一个错误信息
    if (errorInfo.errorFields && errorInfo.errorFields.length > 0) {
      const firstError = errorInfo.errorFields[0];
      if (firstError.errors && firstError.errors.length > 0) {
        toast.error(firstError.errors[0]);
      }
    }
  };

  return (
    <Form
      form={form}
      onFinish={handleFinish}
      onFinishFailed={handleFinishFailed}
    >
      <Form.Item
        name="username"
        rules={[
          { required: true, message: '请输入用户名' },
          { min: 2, message: '用户名至少2个字符' }
        ]}
      >
        <div className="input-wrapper">
          <FaUser className="input-icon" />
          <Input
            placeholder="用户名"
            clearable
          />
        </div>
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码' },
          { min: 6, message: '密码至少6个字符' }
        ]}
      >
        <div className="input-wrapper">
          <FaLock className="input-icon" />
          <Input
            placeholder="密码"
            type="password"
            clearable
          />
        </div>
      </Form.Item>

      <Form.Item>
        <Button
          color="primary"
          loading={loading}
          block
          size="large"
          onClick={() => form.submit()}
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );
};

export default LoginForm; 