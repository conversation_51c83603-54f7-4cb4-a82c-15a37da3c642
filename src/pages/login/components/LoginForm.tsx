import type React from 'react';
import { useState } from 'react';
import { Input, Button } from 'antd-mobile';
import { FaUser, FaLock } from 'react-icons/fa';
import { toast } from 'sonner'
import type { NormalAuthorizationParams } from '#/entity/auth';


interface LoginFormProps {
  onSubmit: (values: NormalAuthorizationParams) => Promise<void>;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<NormalAuthorizationParams>({
    username: '',
    password: '',
  });

  const handleSubmit = async () => {
    // 验证必填字段
    if (!formData.username) {
      toast.error('请输入用户名');
      return;
    }
    if (!formData.password) {
      toast.error('请输入密码');
      return;
    }
    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-form">
      <div className="form-item">
        <div className="input-wrapper">
          <FaUser className="input-icon" />
          <Input
            placeholder="用户名"
            value={formData.username}
            onChange={(value) => setFormData(prev => ({ ...prev, username: value }))}
            clearable
          />
        </div>
      </div>

      <div className="form-item">
        <div className="input-wrapper">
          <FaLock className="input-icon" />
          <Input
            placeholder="密码"
            type="password"
            value={formData.password}
            onChange={(value) => setFormData(prev => ({ ...prev, password: value }))}
            clearable
          />
        </div>
      </div>

      <div className="form-item">
        <Button
          color="primary"
          onClick={handleSubmit}
          loading={loading}
          block
          size="large"
        >
          登录
        </Button>
      </div>

    </div>
  );
};

export default LoginForm; 