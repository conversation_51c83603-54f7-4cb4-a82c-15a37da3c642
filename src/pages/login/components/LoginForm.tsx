import type React from 'react';
import { useState, useEffect } from 'react';
import { Input, Button, Space, Grid, Divider, Toast } from 'antd-mobile';
import { FaUser, FaLock, FaShieldAlt } from 'react-icons/fa';

interface LoginFormValues {
  username: string;
  password: string;
  captcha: string;
}

interface LoginFormProps {
  onSubmit: (values: LoginFormValues) => Promise<void>;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
  const [loading, setLoading] = useState(false);
  const [captcha, setCaptcha] = useState('');
  const [formData, setFormData] = useState<LoginFormValues>({
    username: '',
    password: '',
    captcha: ''
  });

  // 生成随机数字验证码
  const generateCaptcha = () => {
    const newCaptcha = Math.floor(100000 + Math.random() * 900000).toString();
    setCaptcha(newCaptcha);
  };

  useEffect(() => {
    generateCaptcha();
  }, []);

  const handleSubmit = async () => {
    // 验证必填字段
    if (!formData.username) {
      Toast.show('请输入用户名');
      return;
    }
    if (!formData.password) {
      Toast.show('请输入密码');
      return;
    }
    if (!formData.captcha) {
      Toast.show('请输入验证码');
      return;
    }
    if (formData.captcha !== captcha) {
      Toast.show('验证码不正确');
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-form">
      <div className="form-item">
        <div className="input-wrapper">
          <FaUser className="input-icon" />
          <Input
            placeholder="用户名"
            value={formData.username}
            onChange={(value) => setFormData(prev => ({ ...prev, username: value }))}
            clearable
          />
        </div>
      </div>

      <div className="form-item">
        <div className="input-wrapper">
          <FaLock className="input-icon" />
          <Input
            placeholder="密码"
            type="password"
            value={formData.password}
            onChange={(value) => setFormData(prev => ({ ...prev, password: value }))}
            clearable
          />
        </div>
      </div>

      <div className="form-item">
        <Grid columns={2} gap={8}>
          <Grid.Item>
            <div className="input-wrapper">
              <FaShieldAlt className="input-icon" />
              <Input
                placeholder="验证码"
                value={formData.captcha}
                onChange={(value) => setFormData(prev => ({ ...prev, captcha: value }))}
                clearable
              />
            </div>
          </Grid.Item>
          <Grid.Item>
            <Button
              className="captcha-button"
              onClick={generateCaptcha}
              fill="outline"
              size="large"
            >
              {captcha}
            </Button>
          </Grid.Item>
        </Grid>
      </div>

      <div className="form-item">
        <Button
          color="primary"
          onClick={handleSubmit}
          loading={loading}
          block
          size="large"
        >
          登录
        </Button>
      </div>

      <div className="login-footer">
        <Space>
          <Button fill="none" color="primary">忘记密码</Button>
          <Divider direction="vertical" />
          <Button fill="none" color="primary">注册账号</Button>
        </Space>
      </div>
    </div>
  );
};

export default LoginForm; 