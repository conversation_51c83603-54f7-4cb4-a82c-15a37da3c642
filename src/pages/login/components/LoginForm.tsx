import type React from 'react';
import { useState, useEffect } from 'react';
import { Form, Input, Button, Space, Row, Col, Divider } from 'antd';
import { FaUser, FaLock, FaShieldAlt } from 'react-icons/fa';

interface LoginFormValues {
  username: string;
  password: string;
  captcha: string;
}

interface LoginFormProps {
  onSubmit: (values: LoginFormValues) => Promise<void>;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [captcha, setCaptcha] = useState('');

  // 生成随机数字验证码
  const generateCaptcha = () => {
    const newCaptcha = Math.floor(100000 + Math.random() * 900000).toString();
    setCaptcha(newCaptcha);
  };

  useEffect(() => {
    generateCaptcha();
  }, []);

  const handleSubmit = async (values: LoginFormValues) => {
    if (values.captcha !== captcha) {
      form.setFields([
        {
          name: 'captcha',
          errors: ['验证码不正确']
        }
      ]);
      return;
    }

    setLoading(true);
    try {
      await onSubmit(values);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      name="login"
      initialValues={{ remember: true }}
      onFinish={handleSubmit}
      size="large"
      layout="vertical"
    >
      <Form.Item
        name="username"
        rules={[{ required: true, message: '请输入用户名' }]}
      >
        <Input 
          prefix={<FaUser />} 
          placeholder="用户名" 
          autoComplete="username"
        />
      </Form.Item>
      
      <Form.Item
        name="password"
        rules={[{ required: true, message: '请输入密码' }]}
      >
        <Input.Password
          prefix={<FaLock />}
          placeholder="密码"
          autoComplete="current-password"
        />
      </Form.Item>
      
      <Form.Item
        name="captcha"
        rules={[{ required: true, message: '请输入验证码' }]}
      >
        <Row gutter={8}>
          <Col span={16}>
            <Input
              prefix={<FaShieldAlt />}
              placeholder="验证码"
            />
          </Col>
          <Col span={8}>
            <Button 
              className="captcha-button" 
              onClick={generateCaptcha}
            >
              {captcha}
            </Button>
          </Col>
        </Row>
      </Form.Item>
      
      <Form.Item>
        <Button 
          type="primary" 
          htmlType="submit" 
          loading={loading}
          block
        >
          登录
        </Button>
      </Form.Item>
      
      <div className="login-footer">
        <Space>
          <Button type="link">忘记密码</Button>
          <Divider type="vertical" />
          <Button type="link">注册账号</Button>
        </Space>
      </div>
    </Form>
  );
};

export default LoginForm; 