import type { IdpAuthorizationParams, NormalAuthorizationParams } from '#/entity/auth';
import { loginWithIdp } from '@/api/auth';
import useIDP from '@/hooks/use-idp';
import { useAuthStore } from '@/store/authStore';
import type React from 'react';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { LoginForm } from '../components';

const StudentLogin: React.FC = () => {

  const { id, ou, type} = useIDP();
  const { setAuth } = useAuthStore();
  const navigate = useNavigate()
  
  const login = useCallback(async (params: IdpAuthorizationParams) => {
    const { token, tokenType} = await loginWithIdp(params)
    setAuth(`${tokenType} ${token}`)
    navigate('/protected')
  }, [setAuth, navigate])

  useEffect(() => {
    if (id && ou && type) {
      login({
        idpAppType: type,
        idpId: id,
        ou
      })
    }
  }, [id, ou, type, login])

  const handleLoginFormSubmit = async (value: NormalAuthorizationParams) => {
    console.log(value);
  }
  return (
    <div>
      {type}的身份为{id}, 学校{ou}
      <LoginForm onSubmit={handleLoginFormSubmit} />
    </div>
  )
}

export default StudentLogin;
