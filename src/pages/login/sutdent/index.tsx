import type { IdpAuthorizationParams, NormalAuthorizationParams } from '#/entity/auth';
import { loginWithIdp, loginWithPassword } from '@/api/auth';
import useIDP from '@/hooks/use-idp';
import { useAuthStore } from '@/store/authStore';
import type React from 'react';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { LoginForm } from '../components';
import { Card, Divider } from 'antd-mobile';
import '../index.css'

const StudentLogin: React.FC = () => {

  const { id, ou, type} = useIDP();
  const { setAuth } = useAuthStore();
  const navigate = useNavigate()
  
  const login = useCallback(async (params: IdpAuthorizationParams) => {
    const { token, tokenType} = await loginWithIdp(params)
    setAuth(`${tokenType} ${token}`)
    navigate('/protected')
  }, [setAuth, navigate])

  useEffect(() => {
    if (id && ou && type) {
      login({
        idpAppType: type,
        idpId: id,
        ou
      })
    }
  }, [id, ou, type, login])

  const handleLoginFormSubmit = useCallback(async (value: NormalAuthorizationParams) => {
    try {
      const res = await loginWithPassword(value)
      const token = res.data.JwtToken
      if (token) {
        setAuth(res.data.JwtToken)
        navigate('/')
      }
    } catch (error) {
      console.log('异常：', error);
    }
  }, [setAuth, navigate])
  return (
    <div className="login-container">
      <Card className="login-card">
        <div className="login-header">
          <h2 className="login-title">欢迎登录</h2>
          <Divider />
        </div>
        <LoginForm onSubmit={handleLoginFormSubmit} />
      </Card>
    </div>
  )
}

export default StudentLogin;
