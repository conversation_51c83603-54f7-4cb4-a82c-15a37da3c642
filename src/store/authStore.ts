import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthState {
  // 用户是否已认证
  isAuthenticated: boolean;
  // 最后验证时间
  lastVerified: number;
  // token
  token: string | null;
  // 设置认证状态
  setAuth: (token: string) => void;
  // 清除认证状态
  clearAuth: () => void;
  // 检查认证状态
  checkAuth: () => boolean;
}

// 认证状态有效期（毫秒）
const AUTH_VALID_DURATION = 30 * 60 * 1000; // 30分钟

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      lastVerified: 0,
      token: null,
      idp: null,
      
      setAuth: (token: string) => set({
        isAuthenticated: true,
        lastVerified: Date.now(),
        token,
      }),
      
      clearAuth: () => set({
        isAuthenticated: false,
        lastVerified: 0,
        token: null,
      }),
      
      checkAuth: () => {
        const { isAuthenticated, lastVerified, token } = get();
        
        // 如果未认证或没有token，直接返回false
        if (!isAuthenticated || !token) {
          return false;
        }
        
        // 检查认证是否过期
        const now = Date.now();
        const isExpired = now - lastVerified > AUTH_VALID_DURATION;
        
        if (isExpired) {
          // 如果过期，清除认证状态
          get().clearAuth();
          return false;
        }
        
        // 更新最后验证时间（延长会话）
        set({ lastVerified: now });
        return true;
      }
    }),
    {
      name: 'auth-storage',
      // 只持久化这些字段
      partialize: (state) => ({ 
        isAuthenticated: state.isAuthenticated,
        lastVerified: state.lastVerified,
        token: state.token,
      }),
    }
  )
); 