import axios, { type AxiosError, type AxiosResponse, type InternalAxiosRequestConfig } from "axios";
import { parseUrlToObj } from ".";
import { decrypt, encrypt } from "./n-crypto";
import { toast } from 'sonner'
import { useAuthStore } from "@/store/authStore";

// 是否开启加密
const enableCrypto = true;

// 用于存储 URL 和颜色的映射
const urlColorMap = new Map<string, string>();
const colors = [
	"#2196F3", // 蓝色
	"#4CAF50", // 绿色
	"#F44336", // 红色
	"#FF9800", // 橙色
	"#9C27B0", // 紫色
	"#795548", // 棕色
	"#607D8B", // 蓝灰色
	"#E91E63", // 粉色
	"#009688", // 青色
	"#673AB7", // 深紫色
];

// 获取 URL 的基础路径（不包含查询参数）
function getBaseUrl(url: string): string {
	return url.split("?")[0];
}

// 为 URL 获取或分配一个颜色
function getColorForUrl(url: string): string {
	const baseUrl = getBaseUrl(url);
	if (!urlColorMap.has(baseUrl)) {
		const colorIndex = urlColorMap.size % colors.length;
		urlColorMap.set(baseUrl, colors[colorIndex]);
	}
	return urlColorMap.get(baseUrl) || "";
}

function logOnDevOrDebug(url?: string, description?: string, ...params: any[]): void {
	if (!url) {
		return;
	}
	// @ts-ignore
	if (process.env.NODE_ENV === "development" || window.debugmode) {
		const color = getColorForUrl(url);
		const baseUrl = getBaseUrl(url);
		console.log(`%c[${baseUrl + description}]`, `color: ${color}; font-weight: bold;`, ...params);
	}
}

// 拦截get请求，做加密，只需要处理url
export function queryParamsHandler({ config }: { config: InternalAxiosRequestConfig }): InternalAxiosRequestConfig {
	const c = config;
	// 拿到问号之前的url，和问号之后的query
	const [urlWithoutQuery, queryUrl] = config?.url?.split("?") || [];
	// 先用一个对象存储query
	let fullQueryObj: Record<string, any> = {};
	// 如果有问号，就把问号之后的query转换成对象
	if (queryUrl) {
		const queries = parseUrlToObj(`?${queryUrl}`);
		fullQueryObj = { ...queries };
	}
	// 如果有config.params，就把config.params转换成对象
	if (config?.params) {
		fullQueryObj = { ...fullQueryObj, ...config.params };
		c.params = {}; // 把config.params置空，否则会拼接到url后面
	}
	if (Object.keys(fullQueryObj).length === 0) {
		// 如果没有query，就不处理
		return c;
	}
	logOnDevOrDebug(c.url, " 请求 query", fullQueryObj);
	const encryptRes = encrypt(fullQueryObj);
	c.url = `${urlWithoutQuery}?params=${encryptRes}`;
	return c;
}

export function requestBodyHandler({ config }: { config: InternalAxiosRequestConfig }): InternalAxiosRequestConfig {
	let c = config;
	if (config.baseURL !== "/radacct") {
		// url的处理和get一样
		c = queryParamsHandler({ config });
	}
	if (c.data) {
		// 如果有data，就做加密处理
		if (c.data instanceof FormData) {
			// 如果是formdata，就把formdata转换成对象
			const formData = c.data as FormData;
			const formDataObj: Record<string, any> = {};
			formData.forEach((value, key) => {
				formDataObj[key] = value;
			});
			const newFormData = new FormData();
			const encryptRes = encrypt(formDataObj);
			if (encryptRes) {
				newFormData.append("params", encryptRes);
				logOnDevOrDebug(c.url, " 请求 formdata", formDataObj);
			}
			c.data = newFormData;
		} else {
			// 如果不是formdata，就直接做加密处理
			const dataCopied = JSON.parse(JSON.stringify(c.data)); // 深拷贝一份data，否则会影响到原始的data
			const encryptRes = encrypt(c.data);
			if (encryptRes) {
				c.data = encryptRes;
				// @ts-ignore
				c.headers["Content-Type"] = "application/json;charset=UTF-8";
				logOnDevOrDebug(c.url, " 请求 body", dataCopied);
			}
		}
	}
	return c;
}

const encryptRequest = (config: InternalAxiosRequestConfig) => {
	const c = { ...config };

	const isNoEnc = String(c.headers?.["x-safe-level"]) === "0"; // 有这个header就不加密
	if (!enableCrypto || isNoEnc) {
		return c;
	}
	const method = c.method?.toLocaleLowerCase();
	if (method === "get" || method === "delete") {
		return queryParamsHandler({ config: c });
	}
	if (method === "post" || method === "put") {
		return requestBodyHandler({ config: c });
	}
	return config;
};

const appendTokenToHeader = (config: InternalAxiosRequestConfig) => {
	const c = { ...config };
  const { token } = useAuthStore.getState()
	if (token) {
		c.headers.Authorization = token;
	}
	return c;
};
const decryptResponseData = (response: AxiosResponse) => {
	const { data, ...rest } = response;
	if (enableCrypto && typeof data === "string") {
		const decryptJsonStr = decrypt(data);
		if (decryptJsonStr) {
			try {
				const decryptData = JSON.parse(decryptJsonStr);
				logOnDevOrDebug(response.config.url, " 响应 body", decryptData);
				return {
					...rest,
					data: decryptData,
				};
			} catch (err: any) {
				console.log(err);
			}
		}
	}
	return response;
};

const decryptResponseError = (err: AxiosError) => {
	if (err?.response) {
		const encryptData = err.response.data;
		const decryptErr: AxiosError = { ...err };
		if (encryptData && typeof encryptData === "string") {
			const decryptJsonStr = decrypt(encryptData);
			if (decryptJsonStr) {
				try {
					const decryptData = JSON.parse(decryptJsonStr);
					// decryptErr.data = decryptData;
					if (decryptErr.response) {
						decryptErr.response.data = decryptData;
					}
					logOnDevOrDebug(err.request.responseURL, " 响应 err body", decryptData);
				} catch (e: any) {
					console.log(e);
				}
			}
		}
		return Promise.reject(decryptErr);
	}
	return Promise.reject(err);
};

const ToastError = (error: AxiosError<{ description: string }>) => {
	if (error.response?.status === 401) {
		toast.error("登录已过期，请重新登录");
		localStorage.clear();
		window.location.href = "/login";
	} else if (error.response?.status === 400) {
		const description = error.response?.data?.description;
		if (description) {
			toast.error(description);
		}
	}
	return Promise.reject(error);
};

const instance = axios.create({
	baseURL: import.meta.env.VITE_APP_API_BASE_URL,
	timeout: 50000,
});

instance.interceptors.request.use(appendTokenToHeader);
instance.interceptors.request.use(encryptRequest);

instance.interceptors.response.use(decryptResponseData, decryptResponseError);

// 处理 401
instance.interceptors.response.use((response) => response, ToastError);

export default instance;
