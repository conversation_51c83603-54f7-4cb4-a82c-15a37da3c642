import axios from '@/utils/axios';
import type React from 'react';
import type { PropsWithChildren } from 'react';
import { SWRConfig } from 'swr'
const Providers: React.FC<PropsWithChildren> = ({ children }) => {

  return (
    <SWRConfig
      value={{
        fetcher: ([key, params]) => axios({ method: 'get', headers: { 'x-safe-level': 0 }, url: key, params }).then(res => res.data)
      }}
    >
      {children}
    </SWRConfig>
  )
}

export default Providers;
