import axios from '@/utils/axios';
import React, { type PropsWithChildren } from 'react';
import { SWRConfig } from 'swr'
const Providers: React.FC<PropsWithChildren> = ({ children }) => {

  return (
    <SWRConfig
      value={{
        fetcher: ([key, params]) => axios({ method: 'get', url: key, params }).then(res => res.data)
      }}
    >
      {children}
    </SWRConfig>
  )
}

export default Providers;
