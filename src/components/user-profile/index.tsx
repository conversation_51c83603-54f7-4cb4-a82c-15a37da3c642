import type { UserProfiles } from '#/entity/auth';
import { useNLRequestImmutable } from '@/hooks/use-nl-request';
import type React from 'react';
import { BiUser } from 'react-icons/bi';

interface UserProfileProps {
  a?: string
}
const UserProfile: React.FC<UserProfileProps> = (props) => {
  const { data } = useNLRequestImmutable<UserProfiles>(['/api/rest/user/get'])
  const username = data?.userName
  return (
    <div>
      <BiUser />
      {username}
    </div>
  )
}

export default UserProfile;
