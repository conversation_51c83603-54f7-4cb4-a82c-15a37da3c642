import { getIdpAppTypeFromUa } from '@/utils';
import { useNavigate, useSearchParams } from 'react-router'
import type { IdpAppType } from '#/entity/auth'
import { useAuthStore } from '@/store/authStore';
import { getWechatOpenid } from '@/api/auth';
import { useCallback, useEffect, useState } from 'react';

interface IDPCallbackParams {
  code?: string;
  ticket?: string;
  state?: string;
  p?: string; // bas 的加密传参
  appType?: IdpAppType;
  ou?: string;
}


async function getIdpAuthorization({idpAppType, idpCode }: {idpAppType: IdpAppType, idpCode: string, ou?: string}) {
  switch(idpAppType) {
    case 'WECHAT': {
      const { data } = await getWechatOpenid(idpCode)
      return data
    }
    default: {
      return ''
    }
  }
}

function useIDP() {
  const [searchParams] = useSearchParams();
  const { checkAuth } = useAuthStore()
  const [idpId, setIdpId] = useState('');
  const navigate = useNavigate();
  const params = Object.fromEntries(searchParams.entries()) as IDPCallbackParams;
  const idpAppType = params.appType || getIdpAppTypeFromUa()
  const idpCode = params.code || params.ticket
  const ou = params.ou;

  const gotoIdp = useCallback((idpAppType: IdpAppType) => {
    switch(idpAppType) {
        case 'WECHAT': {
          const appId = import.meta.env.VITE_APP_WECHAT_OA_APPID
          const wechatRedirect = encodeURIComponent(window.location.href)
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${wechatRedirect}&response_type=code&scope=snsapi_base#wechat_redirect`;
          break;
        }
        default: {
          break
        }
      }
  }, [])

  useEffect(() => {
    const isAuth = checkAuth();
    if (!isAuth && !idpCode) {
      // 未登录且没有回调
      gotoIdp(idpAppType)
      return
    }
    if (!isAuth && idpCode) {
      // 未登录且有回调参数
      //通过 idpCode 换取用户身份，如果是微信，需要先获取 openid
      getIdpAuthorization({
        idpAppType,
        idpCode,
        ou
      }).then((res) => {
        setIdpId(res)
      })
    }
    if (isAuth) {
      // 已登录
      navigate('/protected')
    }
  }, [checkAuth, idpCode, ou, idpAppType, navigate, gotoIdp])
  return {
    id: idpId,
    type: idpAppType,
    ou
  }
}

export default useIDP