import type { NLResult } from "#/api";
import useSWR, { type SWRConfiguration } from "swr";
import useSWRImmutable from "swr/immutable";

type ArgumentsTuple = readonly [any, ...unknown[]];
type Arguments = string | ArgumentsTuple | Record<any, any> | null | undefined | false;
type Key = Arguments | (() => Arguments);

export function useNLRequest<T = any>(key: Key, config?: SWRConfiguration) {
  const { data, ...rest} = useSWR<NLResult<T>>(key, config)
  return {
    data: data?.Return,
    ...rest
  }
}

export function useNLRequestImmutable<T = any>(key: Key, config?: SWRConfiguration) {
  const { data, ...rest} = useSWRImmutable<NLResult<T>>(key, config)
  return {
    data: data?.Return,
    ...rest
  }
}

export default useNLRequest