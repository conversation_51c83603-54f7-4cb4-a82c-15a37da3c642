import type React from 'react';
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { useAuthStore } from '@/store/authStore';

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { checkAuth } = useAuthStore();
  useEffect(() => {
    // 检查认证状态，这个操作不会每次都调用API，而是使用内存中的状态
    const isAuthenticated = checkAuth();
    
    if (!isAuthenticated) {
      // 用户未认证，重定向到登录页
      const currentPath = encodeURIComponent(location.pathname + location.search);
      navigate(`/login/student?redirect=${currentPath}`, { replace: true });
    }
  }, [checkAuth, navigate, location.pathname, location.search]);

  return children;
};

export default ProtectedRoute;
